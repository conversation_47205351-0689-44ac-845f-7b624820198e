import serial
import serial.tools.list_ports as list_ports
import threading
from PySide6.QtCore import QThread, Signal

def list_serial_ports():
    """
    Scans for available serial ports and returns a list of tuples.
    Each tuple contains the device path and a friendly description.
    """
    if not list_ports:
        return []
    ports = list_ports.comports()
    out = []
    for p in ports:
        try:
            friendly = f"{p.device} — {p.description}"
        except:
            friendly = p.device
        out.append((p.device, friendly))
    return out

class SerialWorker(QThread):
    """
    A QThread to handle all serial communication in a non-blocking manner.
    It reads data from the port and emits signals to the UI.
    """
    data_received = Signal(str)
    status_changed = Signal(str)
    error = Signal(str)

    def __init__(self, port: str, baud: int, eol: bytes = b'\n', parent=None):
        """Initializes the worker with port, baud rate, and end-of-line character."""
        super().__init__(parent)
        self.port_name = port
        self.baud = baud
        self.running = False
        self.ser = None
        self._write_lock = threading.Lock()
        self.eol = eol

    def run(self):
        """The main execution loop for the thread. Attempts to open the port and read data."""
        try:
            self.ser = serial.Serial(self.port_name, self.baud, timeout=0.2)
            self.running = True
            self.status_changed.emit('opened')
            buffer = b''
            while self.running:
                try:
                    data = self.ser.read(1024)
                    if data:
                        try:
                            s = data.decode('utf-8', errors='replace')
                        except:
                            s = repr(data)
                        self.data_received.emit(s)
                    else:
                        self.msleep(50)
                except Exception as e:
                    self.error.emit(f"Read error: {e}")
                    break
        except Exception as e:
            self.status_changed.emit('error')
            self.error.emit(f"Open error: {e}")
        finally:
            self.close_serial()

    def write(self, payload: bytes):
        """Writes data to the serial port in a thread-safe manner."""
        if not self.ser or not self.ser.is_open:
            self.error.emit("Port not open")
            return
        with self._write_lock:
            try:
                self.ser.write(payload)
                self.ser.flush()
            except Exception as e:
                self.error.emit(f"Write error: {e}")

    def close_serial(self):
        """Stops the worker thread and closes the serial port."""
        self.running = False
        if self.ser:
            try:
                self.ser.close()
            except:
                pass
        self.status_changed.emit('closed')

    def change_baud_rate(self, new_baud):
        """Attempts to change the baud rate of the open serial port."""
        if self.ser and self.ser.is_open:
            try:
                self.ser.baudrate = new_baud
                self.baud = new_baud
                return True
            except Exception as e:
                self.error.emit(f"Baud rate change error: {e}")
                return False
        return False
