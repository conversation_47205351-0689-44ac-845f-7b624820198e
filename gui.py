import sys
import os
import json
import re
import webbrowser
from datetime import datetime
from functools import partial

from PySide6.QtCore import Qt, Signal, Slot, QThread, QObject, QTimer
from PySide6.QtGui import QFont, QKeySequence, QAction, QPixmap, QIcon, QTextCursor, QColor
from PySide6.QtWidgets import (
    QApplication, QMainWindow, QWidget, QVBoxLayout, QHBoxLayout, QLabel, QPushButton,
    QTextEdit, QLineEdit, QComboBox, QTabWidget, QCheckBox, QFileDialog, QMessageBox, 
    QToolBar, QSpinBox, QSplitter, QListWidget, QDockWidget, QSizePolicy, QDialog,
    QDialogButtonBox, QFormLayout, QGroupBox, QSlider, QColorDialog, QFrame,
    QScrollArea, QGridLayout, QStyle, QMenu, QInputDialog
)

from serial_worker import SerialWorker, list_serial_ports

def resource_path(relative_path: str) -> str:
    """Return an absolute path to a resource, working for PyInstaller bundles and normal runs."""
    if getattr(sys, 'frozen', False):
        base_path = getattr(sys, '_MEIPASS', os.path.abspath('.'))
    else:
        base_path = os.path.dirname(os.path.abspath(__file__))
    return os.path.join(base_path, relative_path)

class MacroManagerDialog(QDialog):
    """
    A dialog for managing macros, allowing users to add, edit, and delete them.
    """
    def __init__(self, macros_list, parent=None):
        """Initializes the dialog with a list of existing macros."""
        super().__init__(parent)
        self.setWindowTitle("Macro Manager")
        self.setModal(True)
        self.resize(600, 400)
        self.macros = macros_list.copy()
        
        layout = QVBoxLayout(self)
        
        self.macro_list = QListWidget()
        for macro in self.macros:
            self.macro_list.addItem(macro)
        layout.addWidget(self.macro_list)
        
        btn_layout = QHBoxLayout()
        self.btn_add = QPushButton("Add Macro")
        self.btn_edit = QPushButton("Edit")
        self.btn_delete = QPushButton("Delete")
        
        self.btn_add.clicked.connect(self.add_macro)
        self.btn_edit.clicked.connect(self.edit_macro)
        self.btn_delete.clicked.connect(self.delete_macro)
        
        btn_layout.addWidget(self.btn_add)
        btn_layout.addWidget(self.btn_edit)
        btn_layout.addWidget(self.btn_delete)
        btn_layout.addStretch()
        
        layout.addLayout(btn_layout)
        
        self.button_box = QDialogButtonBox(QDialogButtonBox.Ok | QDialogButtonBox.Cancel)
        self.button_box.accepted.connect(self.accept)
        self.button_box.rejected.connect(self.reject)
        layout.addWidget(self.button_box)
    
    def add_macro(self):
        """Opens a dialog to add a new macro."""
        dialog = MacroEditDialog(self)
        if dialog.exec() == QDialog.Accepted:
            name, command = dialog.get_macro()
            macro_text = f"{name} — {command}"
            self.macros.append(macro_text)
            self.macro_list.addItem(macro_text)
    
    def edit_macro(self):
        """Opens a dialog to edit the selected macro."""
        current = self.macro_list.currentItem()
        if not current:
            QMessageBox.warning(self, "No Selection", "Please select a macro to edit.")
            return
        
        current_text = current.text()
        if " — " in current_text:
            name, command = current_text.split(" — ", 1)
        else:
            name, command = current_text, ""
        
        dialog = MacroEditDialog(self, name, command)
        if dialog.exec() == QDialog.Accepted:
            new_name, new_command = dialog.get_macro()
            new_text = f"{new_name} — {new_command}"
            
            row = self.macro_list.currentRow()
            self.macros[row] = new_text
            current.setText(new_text)
    
    def delete_macro(self):
        """Deletes the selected macro after a confirmation."""
        current_row = self.macro_list.currentRow()
        if current_row < 0:
            QMessageBox.warning(self, "No Selection", "Please select a macro to delete.")
            return
        
        reply = QMessageBox.question(self, "Delete Macro", "Are you sure you want to delete this macro?")
        if reply == QMessageBox.Yes:
            self.macros.pop(current_row)
            self.macro_list.takeItem(current_row)

class MacroEditDialog(QDialog):
    """
    A small dialog for entering or editing a single macro's name and command.
    """
    def __init__(self, parent=None, name="", command=""):
        """Initializes the dialog with pre-filled fields if editing."""
        super().__init__(parent)
        self.setWindowTitle("Edit Macro")
        self.setModal(True)
        self.resize(400, 200)
        
        layout = QFormLayout(self)
        
        self.name_edit = QLineEdit(name)
        self.command_edit = QLineEdit(command)
        
        layout.addRow("Macro Name:", self.name_edit)
        layout.addRow("Command:", self.command_edit)
        
        self.button_box = QDialogButtonBox(QDialogButtonBox.Ok | QDialogButtonBox.Cancel)
        self.button_box.accepted.connect(self.accept)
        self.button_box.rejected.connect(self.reject)
        layout.addWidget(self.button_box)
    
    def get_macro(self):
        """Returns the name and command entered by the user."""
        return self.name_edit.text().strip(), self.command_edit.text().strip()

class SerialSessionWidget(QWidget):
    """
    Represents a single serial communication session, contained within a tab.
    Includes terminal, send box, and macros.
    """
    def __init__(self, port, baud=115200, custom_name=None, parent=None):
        """Initializes the session with a port, baud rate, and optional name."""
        super().__init__(parent)
        self.port = port
        self.baud = baud
        self.custom_name = custom_name or port
        self.worker = None
        self._auto_scroll = True
        self._show_timestamps = True
        self.macros = [
            "Hello World — Hello World",
            "AT Command — AT",
            "Reset — RST",
            "Status Check — STATUS?"
        ]
        
        self.setup_ui()
        self.connect()

    def setup_ui(self):
        """Builds the user interface for the session widget."""
        main = QVBoxLayout(self)
        
        control_row = QHBoxLayout()
        main.addLayout(control_row)

        baud_label = QLabel("Baud:")
        control_row.addWidget(baud_label)
        self.baud_combo = QComboBox()
        self.baud_combo.addItems(["9600","19200","38400","57600","115200","230400","460800","921600"])
        self.baud_combo.setCurrentText(str(self.baud))
        self.baud_combo.currentTextChanged.connect(self.change_baud_rate)
        control_row.addWidget(self.baud_combo)

        self.status_label = QLabel(f"{self.port} @ {self.baud}")
        control_row.addWidget(self.status_label)
        control_row.addStretch()

        self.status_icon = QLabel("●")
        self.status_icon.setStyleSheet("color: #ff6b6b;") # Red by default
        control_row.addWidget(self.status_icon)

        self.btn_connect_disconnect = QPushButton("Connect")
        self.btn_connect_disconnect.clicked.connect(self.toggle_connection)
        control_row.addWidget(self.btn_connect_disconnect)

        self.btn_clear = QPushButton("Clear")
        self.btn_clear.clicked.connect(self.clear_terminal)
        control_row.addWidget(self.btn_clear)

        self.btn_save = QPushButton("Save Log")
        self.btn_save.clicked.connect(self.save_log)
        control_row.addWidget(self.btn_save)

        options_row = QHBoxLayout()
        main.addLayout(options_row)

        self.chk_timestamp = QCheckBox("Timestamps")
        self.chk_timestamp.setChecked(self._show_timestamps)
        self.chk_timestamp.stateChanged.connect(self.toggle_timestamps)
        options_row.addWidget(self.chk_timestamp)

        self.chk_autoscroll = QCheckBox("Auto‑scroll")
        self.chk_autoscroll.setChecked(self._auto_scroll)
        self.chk_autoscroll.stateChanged.connect(self.toggle_autoscroll)
        options_row.addWidget(self.chk_autoscroll)

        options_row.addStretch()

        split = QSplitter(Qt.Horizontal)
        main.addWidget(split, 1)

        left = QWidget()
        left_l = QVBoxLayout(left)
        self.terminal = QTextEdit()
        self.terminal.setReadOnly(True)
        monospace = QFont("Courier New", 10)
        self.terminal.setFont(monospace)
        left_l.addWidget(self.terminal, 1)

        send_row = QHBoxLayout()
        self.input_line = QLineEdit()
        self.input_line.returnPressed.connect(self.on_send_pressed)
        send_row.addWidget(self.input_line, 1)
        self.btn_send = QPushButton("Send")
        self.btn_send.clicked.connect(self.on_send_pressed)
        send_row.addWidget(self.btn_send)

        self.chk_hex = QCheckBox("HEX")
        send_row.addWidget(self.chk_hex)
        left_l.addLayout(send_row)
        split.addWidget(left)

        right = QWidget()
        right_l = QVBoxLayout(right)

        self.macro_group = QGroupBox("Macros")
        self.macro_group.setCheckable(True)
        self.macro_group.setChecked(True)
        macro_layout = QVBoxLayout(self.macro_group)
        
        macro_buttons = QHBoxLayout()
        self.btn_manage_macros = QPushButton("Manage")
        self.btn_manage_macros.clicked.connect(self.manage_macros)
        macro_buttons.addWidget(self.btn_manage_macros)
        macro_buttons.addStretch()
        macro_layout.addLayout(macro_buttons)
        
        self.macro_list = QListWidget()
        for macro in self.macros:
            self.macro_list.addItem(macro)
        self.macro_list.itemDoubleClicked.connect(self.on_macro_double)
        macro_layout.addWidget(self.macro_list)
        right_l.addWidget(self.macro_group)

        split.addWidget(right)
        split.setStretchFactor(0, 3)
        split.setStretchFactor(1, 1)

    def change_baud_rate(self, new_baud_str):
        """Attempts to change the baud rate for the active serial connection."""
        try:
            new_baud = int(new_baud_str)
            if self.worker and self.worker.change_baud_rate(new_baud):
                self.baud = new_baud
                self.status_label.setText(f"{self.port} @ {self.baud} (open)")
                self.append_line(f"[Baud rate changed to {new_baud}]", meta='info')
            else:
                self.baud_combo.setCurrentText(str(self.baud))
        except ValueError:
            self.baud_combo.setCurrentText(str(self.baud))

    def manage_macros(self):
        """Opens the macro manager dialog."""
        dialog = MacroManagerDialog(self.macros, self)
        if dialog.exec() == QDialog.Accepted:
            self.macros = dialog.macros
            self.macro_list.clear()
            for macro in self.macros:
                self.macro_list.addItem(macro)

    def connect(self):
        """Initializes and starts the serial communication worker thread."""
        try:
            import serial
        except ImportError:
            self.append_line("[serial not available: install pyserial]", meta='warning')
            self.status_label.setText("Serial unavailable")
            return
        self.worker = SerialWorker(self.port, self.baud)
        self.worker.data_received.connect(self.on_data)
        self.worker.status_changed.connect(self.on_status)
        self.worker.error.connect(self.on_error)
        self.worker.start()
        self.append_line(f"[Opening {self.port} @ {self.baud}]", meta='info')

    def disconnect(self):
        """Initiates disconnection by stopping the worker thread."""
        if self.worker:
            self.worker.close_serial()
            self.worker.quit()
            self.worker.wait(500)

    def toggle_connection(self):
        if self.worker and self.worker.running:
            self.disconnect()
        else:
            self.connect()

    @Slot(str)
    def on_status(self, s):
        """Updates the status label based on the worker's status signal."""
        if s == 'opened':
            self.status_label.setText(f"{self.port} @ {self.baud} (open)")
            self.btn_connect_disconnect.setText("Disconnect")
            self.status_icon.setStyleSheet("color: #2fba00;") # green
        elif s == 'closed':
            self.status_label.setText(f"{self.port} (closed)")
            self.btn_connect_disconnect.setText("Connect")
            self.status_icon.setStyleSheet("color: #ff6b6b;") # red
            self.append_line("[Disconnected]")
        elif s == 'error':
            self.status_label.setText(f"{self.port} (error)")
            self.btn_connect_disconnect.setText("Connect")
            self.status_icon.setStyleSheet("color: #ff6b6b;") # red

    @Slot(str)
    def on_error(self, msg):
        """Displays error messages in the terminal."""
        self.append_line(f"[ERROR] {msg}", meta='error')

    @Slot(str)
    def on_data(self, s: str):
        """Processes and appends received data to the terminal."""
        parts = s.splitlines(True)
        for p in parts:
            self.append_line(p.rstrip('\r\n'))

    def append_line(self, text: str, meta: str = None):
        """Appends a line of text to the terminal, optionally with timestamp and syntax highlighting."""
        cursor = self.terminal.textCursor()
        cursor.movePosition(QTextCursor.End)
        self.terminal.setTextCursor(cursor)

        if self._show_timestamps:
            ts = datetime.now().strftime("%H:%M:%S")
            prefix = f"[{ts}] "
            
            time_format = cursor.charFormat()
            time_format.setForeground(QColor("#8a9496")) # Dim gray for timestamp
            cursor.insertText(prefix, time_format)
        
        text_format = cursor.charFormat()
        if meta == 'error':
            text_format.setForeground(QColor('#ff6b6b'))
        elif meta == 'warning':
            text_format.setForeground(QColor('#ffb86b'))
        elif meta == 'info':
            text_format.setForeground(QColor('#72d6ff'))
        else:
            text_format.setForeground(QColor('#e6edf3'))

        stripped = text.strip()
        pretty = None
        if (stripped.startswith('{') or stripped.startswith('[')) and len(stripped) > 2:
            try:
                obj = json.loads(stripped)
                pretty = json.dumps(obj, indent=2)
            except:
                pretty = None
        
        if pretty:
            cursor.insertText(pretty + '\n', text_format)
        else:
            urlified = re.sub(r"(https?://\S+)", r"<a href='\1'>\1</a>", text)
            cursor.insertHtml(urlified + "<br>")

        if self._auto_scroll:
            self.terminal.ensureCursorVisible()

    def clear_terminal(self):
        """Clears all text from the terminal area."""
        self.terminal.clear()

    def save_log(self):
        """Saves the current terminal content to a text file."""
        fn, _ = QFileDialog.getSaveFileName(self, "Save Log", f"{self.custom_name}_log.txt", 
                                            "Text Files (*.txt);;All Files (*)")
        if not fn:
            return
        try:
            with open(fn, 'w', encoding='utf-8') as f:
                f.write(self.terminal.toPlainText())
            QMessageBox.information(self, "Saved", f"Log saved to {fn}")
        except Exception as e:
            QMessageBox.critical(self, "Error", f"Failed to save log: {e}")

    def on_send_pressed(self):
        """Sends data from the input line, either as text or HEX."""
        text = self.input_line.text()
        if not text:
            return
        if self.chk_hex.isChecked():
            hx = re.sub(r'[^0-9a-fA-F]', '', text)
            if len(hx) % 2 != 0:
                QMessageBox.warning(self, "HEX error", "Odd number of hex digits")
                return
            payload = bytes.fromhex(hx)
        else:
            payload = (text + '\n').encode('utf-8')
        
        if self.worker and self.worker.ser and self.worker.ser.is_open:
            self.worker.write(payload)
            self.append_line(f"[TX] {text}", meta='info')
            self.input_line.clear()
        else:
            QMessageBox.warning(self, "Not connected", "Serial port is not open")

    def toggle_autoscroll(self, state):
        """Toggles the auto-scrolling feature."""
        self._auto_scroll = bool(state)

    def toggle_timestamps(self, state):
        """Toggles the display of timestamps."""
        self._show_timestamps = bool(state)

    def on_macro_double(self, item):
        """Handles a double-click on a macro from the list."""
        txt = item.text()
        if '—' in txt:
            payload = txt.split('—', 1)[1].strip()
        else:
            payload = txt
        self.input_line.setText(payload)
        self.on_send_pressed()

class MainWindow(QMainWindow):
    """
    The main application window for the Universal Serial Monitor.
    Manages tabs, the quick connect dock, and general UI layout.
    """
    def __init__(self):
        """Initializes the main window and its components."""
        super().__init__()
        self.setWindowTitle("Universal Serial Monitor")
        self.resize(1200, 800)
        icon_path = resource_path('app_icon.ico')
        if os.path.exists(icon_path):
            self.setWindowIcon(QIcon(icon_path))
        self.port_refresh_timer = QTimer()
        self.port_refresh_timer.timeout.connect(self.populate_ports)
        self.port_refresh_timer.start(2000)
        self._apply_style()
        self._build_ui()

    def _build_ui(self):
        """Constructs the main window's user interface."""
        toolbar = QToolBar("Main")
        self.addToolBar(toolbar)

        self.tabs = QTabWidget()
        self.tabs.setTabsClosable(True)
        self.tabs.tabCloseRequested.connect(self.close_tab)
        self.tabs.setContextMenuPolicy(Qt.CustomContextMenu)
        self.tabs.customContextMenuRequested.connect(self.show_tab_context_menu)
        self.setCentralWidget(self.tabs)

        dock = QDockWidget("Quick Connect", self)
        dock.setAllowedAreas(Qt.LeftDockWidgetArea | Qt.RightDockWidgetArea)
        self.addDockWidget(Qt.LeftDockWidgetArea, dock)
        dock_widget = QWidget()
        dock.setWidget(dock_widget)
        dl = QVBoxLayout(dock_widget)

        dl.addWidget(QLabel("Port"))
        self.combo_ports = QComboBox()
        dl.addWidget(self.combo_ports)

        dl.addWidget(QLabel("Baud Rate"))
        self.combo_baud = QComboBox()
        self.combo_baud.addItems(["9600","19200","38400","57600","115200","230400","460800","921600"])
        self.combo_baud.setCurrentText("115200")
        dl.addWidget(self.combo_baud)

        dl.addWidget(QLabel("Custom Name"))
        self.custom_name_edit = QLineEdit()
        self.custom_name_edit.setPlaceholderText("Optional tab name")
        dl.addWidget(self.custom_name_edit)

        btn_open = QPushButton("Open Session")
        btn_open.clicked.connect(self.quick_open)
        dl.addWidget(btn_open)

        dl.addStretch()
        
        self.populate_ports()

        self.statusBar().showMessage("Live port refresh active")
        self.create_footer()

        intro = QTextEdit()
        intro.setReadOnly(True)
        intro.setHtml("""
            <div style='padding: 20px;'>
                <h2 style='color:#72d6ff; margin-bottom: 15px;'>Universal Serial Monitor — USM (Enhanced)</h2>
                <h3 style='color:#00ffcc; margin-bottom: 10px;'>New Features:</h3>
                <ul style='color:#c9d7e6; line-height: 1.6;'>
                    <li><b>Live Port Refresh</b> - Ports auto-update every 2 seconds</li>
                    <li><b>Live Baud Rate Change</b> - Change baud rate on-the-fly</li>
                    <li><b>Custom Tab Names</b> - Rename tabs as needed</li>
                    <li><b>Closeable Tabs</b> - Close individual sessions</li>
                    <li><b>Enhanced Macro Management</b> - Add, edit, delete macros</li>
                    <li><b>Collapsible Sections</b> - Hide/show macro sections</li>
                </ul>
                <p style='color:#a9d6e3; margin-top: 20px;'>
                    Select a port from the Quick Connect panel and click <b>Open Session</b> to begin.
                </p>
            </div>
        """)
        self.tabs.addTab(intro, "Welcome")

    def create_footer(self):
        """Creates a persistent footer in the status bar with developer credits."""
        footer_widget = QWidget()
        footer_widget.setStyleSheet("background: transparent;")
        footer_layout = QHBoxLayout(footer_widget)
        footer_layout.setContentsMargins(10, 5, 10, 5)
        
        footer_layout.addStretch()
        
        credit_label = QLabel("developed by Sk Raihan | ")
        credit_label.setStyleSheet("color: #a9d6e3; font-size: 11px;")
        footer_layout.addWidget(credit_label)
        
        lab_link = QLabel('<a href="#" style="color: #72d6ff; text-decoration: none;">SKR Electronics Lab</a>')
        lab_link.setStyleSheet("font-size: 11px;")
        lab_link.mousePressEvent = lambda event: webbrowser.open('http://www.skrelectronicslab.com')
        lab_link.setCursor(Qt.PointingHandCursor)
        footer_layout.addWidget(lab_link)
        
        self.statusBar().addPermanentWidget(footer_widget)

    def _apply_style(self):
        """Applies a dark, modern stylesheet to the entire application."""
        style = """
            QWidget { 
                background: #071027; 
                color: #dbeaf5; 
                font-family: "Segoe UI", Tahoma, Arial; 
            }
            QTabWidget::pane { 
                border: 1px solid #10253a; 
                background: #071027;
            }
            QTabWidget::tab-bar {
                alignment: left;
            }
            QTabBar::tab {
                background: #0b1e35;
                color: #a9d6e3;
                padding: 8px 15px;
                margin-right: 2px;
                border: 1px solid #10253a;
                border-bottom: none;
                border-radius: 6px 6px 0 0;
            }
            QTabBar::tab:selected {
                background: #071027;
                color: #72d6ff;
                border-bottom: 2px solid #72d6ff;
            }
            QTabBar::tab:hover {
                background: #135c84;
                color: #e6f7ff;
            }
            QTabBar::close-button {
                image: url(data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIxNiIgaGVpZ2h0PSIxNiIgdmlld0JveD0iMCAwIDI0IDI0IiBmaWxsPSJub25lIiBzdHJva2U9IndoaXRlIiBzdHJva2Utd2lkdGg9IjMiIHN0cm9rZS1saW5lY2FwPSJyb3VuZCIgc3Ryb2tlLWxpbmVqb2luPSJyb3VuZCI+PGxpbmUgeDE9IjE4IiB5MT0iNiIgeDI9IjYiIHkyPSIxOCIvPjxsaW5lIHgxPSI2IiB5MT0iNiIgeDI9IjE4IiB5Mj0iMTgiLz48L3N2Zz4=);
                background: transparent;
                border-radius: 3px;
            }
            QTabBar::close-button:hover {
                background: #135c84;
            }

            QTextEdit { 
                background: #061224; 
                border: 1px solid #10253a;
                selection-background-color: #135c84;
            }
            QLineEdit { 
                background: #061224; 
                border: 1px solid #10253a;
                padding: 5px;
                border-radius: 4px;
            }
            QLineEdit:focus {
                border: 1px solid #72d6ff;
            }
            QPushButton { 
                background: #0b3b5b; 
                border: 1px solid #10253a;
                border-radius: 6px; 
                padding: 8px 12px; 
                color: #e6f7ff;
                font-weight: bold;
            }
            QPushButton:hover { 
                background: #135c84;
                border: 1px solid #72d6ff;
            }
            QPushButton:pressed {
                background: #0a2a4a;
            }
            QComboBox { 
                background: #061224; 
                border: 1px solid #10253a; 
                padding: 5px;
                border-radius: 4px;
            }
            QComboBox:focus {
                border: 1px solid #72d6ff;
            }
            QComboBox::drop-down {
                border: none;
                padding-right: 10px;
            }
            QComboBox::down-arrow {
                image: url(data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTIiIGhlaWdodD0iOCIgdmlld0JveD0iMCAwIDEyIDgiIGZpbGw9Im5vbmUiPgo8cGF0aCBkPSJNMSAxTDYgNkwxMSAxIiBzdHJva2U9IiM3MmQ2ZmYiIHN0cm9rZS13aWR0aD0iMiIgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIi8+Cjwvc3ZnPg==);
            }
            QLabel { 
                color: #a9d6e3; 
            }
            QCheckBox {
                color: #a9d6e3;
            }
            QCheckBox::indicator {
                width: 16px;
                height: 16px;
                border: 1px solid #10253a;
                border-radius: 3px;
                background: #061224;
            }
            QCheckBox::indicator:checked {
                image: url(data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIxNiIgaGVpZ2h0PSIxNiIgdmlld0JveD0iMCAwIDI0IDI0IiBmaWxsPSJub25lIiBzdHJva2U9IiMyZmJhMDAiIHN0cm9rZS13aWR0aD0iMyIgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIiBzdHJva2UtbGluZWpvaW49InJvdW5kIj48cG9seWxpbmUgcG9pbnRzPSIyMCA2IDkgMTcgNCAxMiIvPjwvc3ZnPg==);
                background-color: #071027;
                border: 1px solid #2fba00;
            }
            QGroupBox {
                color: #72d6ff;
                font-weight: bold;
                border: 1px solid #10253a;
                border-radius: 6px;
                margin-top: 12px;
                padding-top: 8px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
            }
            QGroupBox::indicator {
                width: 16px;
                height: 16px;
            }
            QGroupBox::indicator:unchecked {
                image: url(data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTYiIGhlaWdodD0iMTYiIHZpZXdCb3g9IjAgMCAxNiAxNiIgZmlsbD0ibm9uZSI+CjxyZWN0IHg9IjIiIHk9IjIiIHdpZHRoPSIxMiIgaGVpZ2h0PSIxMiIgc3Ryb2tlPSIjMTA1MzNhIiBzdHJva2Utd2lkdGg9IjIiIHJ4PSIyIi8+Cjwvc3ZnPg==);
            }
            QGroupBox::indicator:checked {
                image: url(data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTYiIGhlaWdodD0iMTYiIHZpZXdCb3g9IjAgMCAxNiAxNiIgZmlsbD0ibm9uZSI+CjxyZWN0IHg9IjIiIHk9IjIiIHdpZHRoPSIxMiIgaGVpZ2h0PSIxMiIgZmlsbD0iIzcyZDZmZiIgc3Ryb2tlPSIjNzJkNmZmIiBzdHJva2Utd2lkdGg9IjIiIHJ4PSIyIi8+CjxwYXRoIGQ9Ik01IDhMNyAxMEwxMSA2IiBzdHJva2U9IiMwNzEwMjciIHN0cm9rZS13aWR0aD0iMiIgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIiBzdHJva2UtbGluZWpvaW49InJvdW5kIi8+Cjwvc3ZnPg==);
            }
            QListWidget {
                background: #061224;
                border: 1px solid #10253a;
                border-radius: 4px;
            }
            QListWidget::item {
                padding: 5px;
                border-bottom: 1px solid #10253a;
            }
            QListWidget::item:selected {
                background: #135c84;
                color: #e6f7ff;
            }
            QListWidget::item:hover {
                background: #0b3b5b;
            }
            QDockWidget { 
                background: #071027; 
                border: 1px solid #10253a;
                titlebar-close-icon: url(close.png);
                titlebar-normal-icon: url(float.png);
            }
            QDockWidget::title {
                background: #0b3b5b;
                color: #72d6ff;
                padding: 8px;
                font-weight: bold;
            }
            QToolBar {
                background: #0b1e35;
                border: 1px solid #10253a;
                spacing: 3px;
                padding: 5px;
            }
            QToolBar QToolButton {
                background: #0b3b5b;
                border: 1px solid #10253a;
                border-radius: 4px;
                padding: 6px 10px;
                color: #e6f7ff;
            }
            QToolBar QToolButton:hover {
                background: #135c84;
                border: 1px solid #72d6ff;
            }
            QStatusBar {
                background: #0b1e35;
                border-top: 1px solid #10253a;
                color: #a9d6e3;
            }
            QSplitter::handle {
                background: #10253a;
                width: 3px;
                margin: 2px;
            }
            QSplitter::handle:hover {
                background: #72d6ff;
            }
            QSpinBox {
                background: #061224;
                border: 1px solid #10253a;
                padding: 4px;
                border-radius: 4px;
            }
            QSpinBox:focus {
                border: 1px solid #72d6ff;
            }
        """
        self.setStyleSheet(style)

    def populate_ports(self):
        """Refreshes the list of available serial ports in the combo box."""
        current_ports = [self.combo_ports.itemText(i) for i in range(self.combo_ports.count())]
        new_ports = list_serial_ports()
        new_port_names = [p[1] for p in new_ports]
        
        if new_port_names != current_ports:
            self.combo_ports.clear()
            for device, friendly in new_ports:
                self.combo_ports.addItem(friendly, device)
            
            if new_ports:
                self.statusBar().showMessage(f"Found {len(new_ports)} serial ports.")
            else:
                self.statusBar().showMessage("No serial ports found.")

    def quick_open(self):
        """Creates and opens a new session tab based on the quick connect panel's settings."""
        idx = self.combo_ports.currentIndex()
        if idx == -1:
            QMessageBox.warning(self, "No Port Selected", "Please select a serial port to open.")
            return

        port_device = self.combo_ports.itemData(idx)
        baud_rate = int(self.combo_baud.currentText())
        custom_name = self.custom_name_edit.text()

        for i in range(self.tabs.count()):
            widget = self.tabs.widget(i)
            if isinstance(widget, SerialSessionWidget) and widget.port == port_device:
                QMessageBox.information(self, "Session Already Open", "A session for this port is already open.")
                self.tabs.setCurrentIndex(i)
                return

        new_tab = SerialSessionWidget(port_device, baud_rate, custom_name, parent=self.tabs)
        tab_name = custom_name or port_device
        tab_index = self.tabs.addTab(new_tab, tab_name)
        self.tabs.setCurrentWidget(new_tab)

        checkbox = QCheckBox()
        self.tabs.tabBar().setTabButton(tab_index, QTabBar.ButtonPosition.RightSide, checkbox)

    def close_tab(self, index):
        """Closes a single tab and disconnects the associated serial session."""
        widget = self.tabs.widget(index)
        if isinstance(widget, SerialSessionWidget):
            widget.disconnect()
        self.tabs.removeTab(index)
        widget.deleteLater()

    def clear_all_tabs(self):
        """Closes all open session tabs."""
        reply = QMessageBox.question(self, "Clear All Sessions", 
                                     "Are you sure you want to close all open sessions?",
                                     QMessageBox.Yes | QMessageBox.No)
        if reply == QMessageBox.Yes:
            for i in range(self.tabs.count() - 1, -1, -1):
                self.close_tab(i)

    def show_tab_context_menu(self, position):
        index = self.tabs.tabBar().tabAt(position)
        if index > 0: # Do not show context menu for welcome tab
            menu = QMenu(self)
            rename_action = menu.addAction("Rename Tab")
            action = menu.exec(self.tabs.tabBar().mapToGlobal(position))

            if action == rename_action:
                widget = self.tabs.widget(index)
                if isinstance(widget, SerialSessionWidget):
                    current_name = self.tabs.tabText(index)
                    new_name, ok = QInputDialog.getText(self, "Rename Tab", "Enter new name:", QLineEdit.Normal, current_name)
                    if ok and new_name:
                        self.update_tab_name(widget, new_name)

    def update_tab_name(self, widget, name):
        """Updates the title of a tab when its session's name is changed."""
        index = self.tabs.indexOf(widget)
        if index != -1:
            widget.custom_name = name
            self.tabs.setTabText(index, name)

    def open_new_session_dialog(self):
        """A placeholder method for a future dialog to configure a new session."""
        QMessageBox.information(self, "New Session", "Use the 'Quick Connect' panel on the left to open a new session.")